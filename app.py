#!/usr/bin/env python3
from os import path

import aws_cdk as cdk


from insighter.cdk.stack import BusinessIntegrationInsighterStack
from multi_key_value_reports.cdk.stack import BusinessIntegrationMultiKeyValueReportStack

app = cdk.App()

ROOT_DIR = path.dirname(path.abspath(__file__))

BusinessIntegrationInsighterStack(
    app,
    "InsighterStack",
    ROOT_DIR,
    env=cdk.Environment(account='************', region='eu-central-1')
)

BusinessIntegrationMultiKeyValueReportStack(
    app,
    "InsighterMultiKeyValueStack",
    ROOT_DIR,
    env=cdk.Environment(account='************', region='eu-central-1')
)

app.synth()
