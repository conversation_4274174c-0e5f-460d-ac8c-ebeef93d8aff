name: Build
on:
  push:
  pull_request:
    types: [opened, synchronize, reopened]
jobs:
  tests:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions/setup-python@v5
        with:
          python-version: '3.13'
      - name: Install dev dependencies
        run: |
          python3 -m pip install setuptools
          pip install -r ${{ github.workspace }}/requirements-dev.txt
      - name: Tests
        run: python -m unittest discover tests
