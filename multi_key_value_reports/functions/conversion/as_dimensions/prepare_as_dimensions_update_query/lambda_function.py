def lambda_handler(event, context):
    """
    Lambda function to prepare AS Dimensions update query for corrected data.
    
    This function implements point 3 from code_assistant.md:
    - Extracts partition information from Step Function input
    - Constructs the Google partition string
    - Substitutes the placeholder in the Athena query template
    - Returns the complete Athena query string
    
    Args:
        event (dict): Step Function input containing:
            - partition_0: year (e.g., "2023")
            - partition_1: month (e.g., "10") 
            - partition_2: day (e.g., "27")
            - partition_3: hour (e.g., "15")
            - bucket_name: S3 bucket name
            
    Returns:
        dict: Event with added query information
    """
    
    # Extract partition information from the event
    partition_0 = event['partition_0']  # year
    partition_1 = event['partition_1']  # month
    partition_2 = event['partition_2']  # day
    partition_3 = event['partition_3']  # hour
    
    # Construct the Google partition string as specified in code_assistant.md
    # Format: "YYYY-MM-DD HH"
    input_google_partition_string = f"{partition_0}-{partition_1}-{partition_2} {partition_3}"
    
    # Athena query template from code_assistant.md with placeholder substitution
    query_string = f"""
-- This query finds the as_dimensions partition (m_partition) and the set of
-- Google partitions (original_g_partitions_used) that were originally combined
-- to create that as_dimensions entry, based on a single input Google partition.

WITH distinct_g_partitions_by_m_partition AS (
    -- This CTE groups by the as_dimensions partition (date, hour)
    -- and aggregates all unique Google partitions (reconstructed from g_partition_0..3)
    -- that contributed data to that specific as_dimensions partition.
    SELECT
        date,
        hour,
        ARRAY_AGG(
            DISTINCT concat(g_partition_0, '-', g_partition_1, '-', g_partition_2, ' ', g_partition_3)
        ) AS actual_g_partitions_used
    FROM
        as_dimensions
    -- Optional: Add a date filter here if your as_dimensions table is very large
    -- and you expect corrected data to be for recent partitions.
    -- This can significantly reduce the amount of data scanned.
    -- WHERE
    --    date_parse(date, '%Y-%m-%d') >= date_add('day', -7, current_date) -- Example: look back 7 days
    GROUP BY
        date,
        hour
)
SELECT
    concat(date, ' ', hour) AS m_partition_to_update, -- This is the Microsoft partition (date hour)
    actual_g_partitions_used AS original_g_partitions_used -- These are the Google partitions originally used
FROM
    distinct_g_partitions_by_m_partition
WHERE
    -- Check if the array of Google partitions (actual_g_partitions_used)
    -- for this m_partition contains the specific Google partition string
    -- that triggered this process.
    contains(actual_g_partitions_used, '{input_google_partition_string}')
"""
    
    # Add the query to the event
    event['query'] = {
        'QueryString': query_string.strip()
    }
    
    # Add metadata about the operation and input
    event['operation'] = 'find_affected_as_dimensions_partitions'
    event['input_google_partition_string'] = input_google_partition_string
    event['google_partition_info'] = {
        'partition_0': partition_0,
        'partition_1': partition_1,
        'partition_2': partition_2,
        'partition_3': partition_3
    }
    
    return event
