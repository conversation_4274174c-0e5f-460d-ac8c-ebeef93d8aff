def lambda_handler(event, context):
    """
    Lambda function to prepare a DELETE query for AS Dimensions corrected data.
    
    This function creates a query to delete existing AS Dimensions data for the 
    corrected Google partitions before new corrected data is processed.
    
    Args:
        event (dict): Event containing corrected partition information:
            - partition_0: year (e.g., "2024")
            - partition_1: month (e.g., "10") 
            - partition_2: day (e.g., "15")
            - partition_3: hour (e.g., "14")
            - source_table: the Google table that was corrected
            - bucket_name: S3 bucket name
            
    Returns:
        dict: Event with added query information
    """
    
    # Extract partition information from the event
    partition_0 = event['partition_0']  # year
    partition_1 = event['partition_1']  # month
    partition_2 = event['partition_2']  # day
    partition_3 = event['partition_3']  # hour
    
    # Create the date string for the AS Dimensions table
    date_string = f"{partition_0}-{partition_1}-{partition_2}"
    
    # Prepare DELETE query to remove existing AS Dimensions data for the corrected partitions
    # We need to delete data where the Google partitions match the corrected partitions
    query_string = (
        "DELETE FROM as_dimensions "
        "WHERE "
        f"    g_partition_0 = '{partition_0}' "
        f"    AND g_partition_1 = '{partition_1}' "
        f"    AND g_partition_2 = '{partition_2}' "
        f"    AND g_partition_3 = '{partition_3}' "
        f"    AND date = '{date_string}' "
        f"    AND hour = '{partition_3}'"
    )
    
    # Add the query to the event
    event['query'] = {
        'QueryString': query_string
    }
    
    # Add metadata about the operation
    event['operation'] = 'delete_corrected_partitions'
    event['affected_partitions'] = {
        'g_partition_0': partition_0,
        'g_partition_1': partition_1,
        'g_partition_2': partition_2,
        'g_partition_3': partition_3,
        'date': date_string,
        'hour': partition_3
    }
    
    return event
