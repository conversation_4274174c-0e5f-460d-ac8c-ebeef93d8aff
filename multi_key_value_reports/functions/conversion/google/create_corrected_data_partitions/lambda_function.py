import boto3
import json
import os

s3_client = boto3.client('s3')

def lambda_handler(event, context):
    """
    Lambda function to store corrected data partitions in S3.
    
    This function creates or updates an object in the processing bucket with a prefix of
    "tmp/corrected_data/" and an object name in the format "yyyy-mm-dd hh", where
    yyyy, mm, dd, and hh are partition_0, partition_1, partition_2, and partition_3
    from the corrected data file.
    
    The object content is an array of JSON objects. When there is already an object
    with the same partitions, the function checks if the current JSON object is not
    already in the array and adds it if it's not.
    
    Args:
        event (dict): The event containing the partition information and data to store
        context (LambdaContext): The Lambda context
        
    Returns:
        dict: The input event, unmodified
    """
    # Get the bucket name from environment variables
    bucket_name = os.environ.get('BUCKET_NAME')
    
    # Extract partition values from the event
    partition_0 = event['partition_0']  # year
    partition_1 = event['partition_1']  # month
    partition_2 = event['partition_2']  # day
    partition_3 = event['partition_3']  # hour
    
    # Create the object key in the format "tmp/corrected_data/yyyy-mm-dd hh"
    object_key = f"tmp/corrected_data/{partition_0}-{partition_1}-{partition_2} {partition_3}"
    
    # The current JSON object to add to the array
    current_json_object = event
    
    try:
        # Check if the object already exists
        try:
            response = s3_client.get_object(Bucket=bucket_name, Key=object_key)
            # If the object exists, read its content
            existing_content = json.loads(response['Body'].read().decode('utf-8'))
            
            # Check if the current JSON object is already in the array
            # We'll use a simple equality check for the entire object
            if not any(json.dumps(obj, sort_keys=True) == json.dumps(current_json_object, sort_keys=True) 
                      for obj in existing_content):
                # Add the current JSON object to the array
                existing_content.append(current_json_object)
                
                # Write the updated array back to S3
                s3_client.put_object(
                    Bucket=bucket_name,
                    Key=object_key,
                    Body=json.dumps(existing_content)
                )
        except s3_client.exceptions.NoSuchKey:
            # If the object doesn't exist, create a new array with the current JSON object
            s3_client.put_object(
                Bucket=bucket_name,
                Key=object_key,
                Body=json.dumps([current_json_object])
            )
    except Exception as e:
        # Log any errors
        print(f"Error processing corrected data partitions: {str(e)}")
        raise
    
    # Return the input event
    return event
