import boto3
import json
import os
import logging
from datetime import datetime, timedelta
from botocore.exceptions import ClientError

# Set up logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
dynamodb = boto3.resource('dynamodb')
step_functions_client = boto3.client('stepfunctions')
events_client = boto3.client('events')

# Required file types for complete processing
REQUIRED_FILE_TYPES = {
    'network_impressions',
    'network_backfill_impressions',
    'network_clicks',
    'network_backfill_clicks',
    'network_active_views',
    'network_backfill_active_views'
}

def lambda_handler(event, context):
    """
    Lambda function to coordinate Google corrected feed processing using DynamoDB.

    This function tracks when all 6 required file types have been processed for a given
    time partition and triggers the MKVR update only when complete data is available.

    Args:
        event (dict): The event containing partition and table information
        context (LambdaContext): The Lambda context

    Returns:
        dict: Processing result with completion status
    """
    try:
        # Extract partition information from the event
        partition_0 = event['partition_0']  # year
        partition_1 = event['partition_1']  # month
        partition_2 = event['partition_2']  # day
        partition_3 = event['partition_3']  # hour
        table_name = event['table_name']

        # Create partition key in format YYYY-MM-DD-HH
        partition_key = f"{partition_0}-{partition_1}-{partition_2}-{partition_3}"

        logger.info(f"Processing corrected data for partition: {partition_key}, table: {table_name}")

        # Get DynamoDB table
        table = dynamodb.Table(os.environ['COORDINATION_TABLE_NAME'])

        # Calculate TTL (7 days from now)
        ttl_timestamp = int((datetime.utcnow() + timedelta(days=7)).timestamp())
        current_timestamp = int(datetime.utcnow().timestamp())

        # Add the current file type to the processed set using conditional update
        try:
            response = table.update_item(
                Key={'partition_key': partition_key},
                UpdateExpression='ADD processed_file_types :file_type SET last_updated = :timestamp, #ttl = :ttl',
                ConditionExpression='attribute_exists(partition_key)',
                ExpressionAttributeNames={
                    '#ttl': 'ttl'
                },
                ExpressionAttributeValues={
                    ':file_type': {table_name},
                    ':timestamp': current_timestamp,
                    ':ttl': ttl_timestamp
                },
                ReturnValues='ALL_NEW'
            )
            logger.info(f"Updated existing record for partition {partition_key}")

        except ClientError as e:
            if e.response['Error']['Code'] == 'ConditionalCheckFailedException':
                # Record doesn't exist, create it
                try:
                    response = table.put_item(
                        Item={
                            'partition_key': partition_key,
                            'processed_file_types': {table_name},
                            'created_timestamp': current_timestamp,
                            'last_updated': current_timestamp,
                            'ttl': ttl_timestamp
                        },
                        ConditionExpression='attribute_not_exists(partition_key)'
                    )
                    logger.info(f"Created new record for partition {partition_key}")

                    # Get the item to check completion
                    response = table.get_item(Key={'partition_key': partition_key})

                except ClientError as create_error:
                    if create_error.response['Error']['Code'] == 'ConditionalCheckFailedException':
                        # Another process created the record, retry the update
                        response = table.update_item(
                            Key={'partition_key': partition_key},
                            UpdateExpression='ADD processed_file_types :file_type SET last_updated = :timestamp, #ttl = :ttl',
                            ExpressionAttributeNames={
                                '#ttl': 'ttl'
                            },
                            ExpressionAttributeValues={
                                ':file_type': {table_name},
                                ':timestamp': current_timestamp,
                                ':ttl': ttl_timestamp
                            },
                            ReturnValues='ALL_NEW'
                        )
                        logger.info(f"Retried update for partition {partition_key}")
                    else:
                        raise create_error
            else:
                raise e

        # Check if all required file types have been processed
        if 'Attributes' in response:
            processed_file_types = response['Attributes']['processed_file_types']
        else:
            # For put_item, get the current state
            get_response = table.get_item(Key={'partition_key': partition_key})
            processed_file_types = get_response['Item']['processed_file_types']

        logger.info(f"Processed file types for {partition_key}: {processed_file_types}")

        # Check if we have all required file types
        if REQUIRED_FILE_TYPES.issubset(processed_file_types):
            logger.info(f"All 6 file types processed for partition {partition_key}. Triggering MKVR update.")

            # Trigger the MKVR update step function
            step_function_input = {
                "bucket_name": event['bucket_name'],
                "table_to_reload": os.environ['TABLE_MULTI_KEY_VALUE_GOOGLE'],
                "partition_0": partition_0,
                "partition_1": partition_1,
                "partition_2": partition_2,
                "partition_3": partition_3,
                "path": f"{os.environ['TABLE_MULTI_KEY_VALUE_GOOGLE']}/partition_0={partition_0}/partition_1={partition_1}/partition_2={partition_2}/partition_3={partition_3}",
                "query": {}
            }

            response = step_functions_client.start_execution(
                stateMachineArn=os.environ['UPDATE_MKVR_STEP_FUNCTION_ARN'],
                input=json.dumps(step_function_input)
            )

            execution_arn = response['executionArn']
            logger.info(f"Started MKVR update step function: {execution_arn}")

            # Wait for the step function to complete
            waiter = step_functions_client.get_waiter('execution_succeeded')
            try:
                waiter.wait(
                    executionArn=execution_arn,
                    WaiterConfig={
                        'Delay': 30,  # Check every 30 seconds
                        'MaxAttempts': 60  # Wait up to 30 minutes
                    }
                )
                logger.info(f"MKVR update step function completed successfully: {execution_arn}")

                # Send EventBridge event to signal AS Dimensions update
                events_client.put_events(
                    Entries=[
                        {
                            'Source': 'insighter.google.feed.corrected',
                            'DetailType': 'GoogleDataCorrectionProcessed',
                            'Detail': json.dumps({
                                'partition_0': partition_0,
                                'partition_1': partition_1,
                                'partition_2': partition_2,
                                'partition_3': partition_3,
                                'source_table': table_name,
                                'bucket_name': event['bucket_name'],
                                'execution_arn': execution_arn
                            })
                        }
                    ]
                )
                logger.info(f"Sent EventBridge event for AS Dimensions update")

                # Delete the coordination record since processing is complete
                table.delete_item(Key={'partition_key': partition_key})
                logger.info(f"Deleted coordination record for partition {partition_key}")

                return {
                    'statusCode': 200,
                    'body': {
                        'message': 'All file types processed, MKVR update completed, AS Dimensions notified',
                        'partition_key': partition_key,
                        'execution_arn': execution_arn,
                        'processed_file_types': list(processed_file_types),
                        'complete': True
                    }
                }

            except Exception as wait_error:
                logger.error(f"MKVR update step function failed or timed out: {str(wait_error)}")
                # Don't delete the coordination record if the step function failed
                return {
                    'statusCode': 500,
                    'body': {
                        'message': 'MKVR update step function failed',
                        'partition_key': partition_key,
                        'execution_arn': execution_arn,
                        'error': str(wait_error),
                        'complete': False
                    }
                }
        else:
            missing_types = REQUIRED_FILE_TYPES - processed_file_types
            logger.info(f"Waiting for more file types. Missing: {missing_types}")

            return {
                'statusCode': 200,
                'body': {
                    'message': 'Waiting for more file types',
                    'partition_key': partition_key,
                    'processed_file_types': list(processed_file_types),
                    'missing_file_types': list(missing_types),
                    'complete': False
                }
            }

    except Exception as e:
        logger.error(f"Error coordinating corrected data processing: {str(e)}")
        raise
