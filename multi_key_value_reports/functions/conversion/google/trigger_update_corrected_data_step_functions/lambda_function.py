import boto3
import json
import os
import logging

# Set up logging
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Initialize AWS clients
s3_client = boto3.client('s3')
step_functions_client = boto3.client('stepfunctions')

def lambda_handler(event, context):
    """
    Lambda function to trigger update_mkvr_step_function when a corrected data array reaches 6 objects.
    
    This function is triggered by S3 PutObject events with the prefix "tmp/corrected_data/".
    It checks if the JSON array in the S3 object contains 6 JSON objects. If it does,
    it triggers the update_mkvr_step_function and waits for its execution result.
    If the step function execution is successful, it deletes the S3 object.
    If the step function execution fails, it logs the error and leaves the S3 object intact.
    
    Args:
        event (dict): The S3 event notification
        context (LambdaContext): The Lambda context
        
    Returns:
        dict: Information about the processing result
    """
    try:
        # Extract bucket and key from the S3 event
        bucket_name = event['Records'][0]['s3']['bucket']['name']
        object_key = event['Records'][0]['s3']['object']['key']
        
        logger.info(f"Processing S3 object: {bucket_name}/{object_key}")
        
        # Get the object content
        response = s3_client.get_object(Bucket=bucket_name, Key=object_key)
        content = json.loads(response['Body'].read().decode('utf-8'))
        
        # Check if the array contains 6 JSON objects
        if not isinstance(content, list):
            logger.warning(f"S3 object {object_key} does not contain a JSON array")
            return {
                'statusCode': 400,
                'body': 'S3 object does not contain a JSON array'
            }
        
        logger.info(f"Found {len(content)} JSON objects in the array")
        
        # If the array has less than 6 objects, exit
        if len(content) < 6:
            logger.info(f"Array has {len(content)} objects, waiting for more data")
            return {
                'statusCode': 200,
                'body': f'Array has {len(content)} objects, waiting for more data'
            }
        
        # Extract partition information from the first object in the array
        # Assuming all objects in the array have the same partition values
        first_object = content[0]
        partition_0 = first_object['partition_0']  # year
        partition_1 = first_object['partition_1']  # month
        partition_2 = first_object['partition_2']  # day
        partition_3 = first_object['partition_3']  # hour
        
        # Prepare input for the step function
        step_function_input = {
            "bucket_name": bucket_name,
            "table_to_reload": os.environ.get('TABLE_MULTI_KEY_VALUE_GOOGLE'),
            "partition_0": partition_0,
            "partition_1": partition_1,
            "partition_2": partition_2,
            "partition_3": partition_3,
            "path": f"{os.environ.get('TABLE_MULTI_KEY_VALUE_GOOGLE')}/partition_0={partition_0}/partition_1={partition_1}/partition_2={partition_2}/partition_3={partition_3}",
            "query": {}
        }
        
        # Start the step function execution
        logger.info(f"Starting step function execution with input: {json.dumps(step_function_input)}")
        response = step_functions_client.start_execution(
            stateMachineArn=os.environ.get('UPDATE_MKVR_STEP_FUNCTION_ARN'),
            input=json.dumps(step_function_input)
        )
        
        execution_arn = response['executionArn']
        logger.info(f"Step function execution started with ARN: {execution_arn}")
        
        # Wait for the step function execution to complete
        execution_result = wait_for_step_function_execution(execution_arn)
        
        # If the step function execution was successful, delete the S3 object
        if execution_result['status'] == 'SUCCEEDED':
            logger.info(f"Step function execution succeeded, deleting S3 object: {bucket_name}/{object_key}")
            s3_client.delete_object(Bucket=bucket_name, Key=object_key)
            return {
                'statusCode': 200,
                'body': 'Step function execution succeeded, S3 object deleted'
            }
        else:
            # If the step function execution failed, log the error and leave the S3 object intact
            logger.error(f"Step function execution failed with status: {execution_result['status']}")
            logger.error(f"Error: {execution_result.get('error', 'Unknown error')}")
            logger.error(f"Cause: {execution_result.get('cause', 'Unknown cause')}")
            return {
                'statusCode': 500,
                'body': f"Step function execution failed with status: {execution_result['status']}"
            }
    
    except Exception as e:
        logger.error(f"Error processing S3 object: {str(e)}")
        raise

def wait_for_step_function_execution(execution_arn, max_attempts=60, delay=5):
    """
    Wait for a Step Functions execution to complete.
    
    Args:
        execution_arn (str): The ARN of the Step Functions execution
        max_attempts (int): Maximum number of attempts to check the execution status
        delay (int): Delay in seconds between attempts
        
    Returns:
        dict: The execution result
    """
    import time
    
    logger.info(f"Waiting for step function execution to complete: {execution_arn}")
    
    for attempt in range(max_attempts):
        response = step_functions_client.describe_execution(
            executionArn=execution_arn
        )
        
        status = response['status']
        logger.info(f"Execution status (attempt {attempt+1}/{max_attempts}): {status}")
        
        if status in ['SUCCEEDED', 'FAILED', 'TIMED_OUT', 'ABORTED']:
            logger.info(f"Execution completed with status: {status}")
            result = {
                'status': status
            }
            
            if status == 'FAILED':
                result['error'] = response.get('error', 'Unknown error')
                result['cause'] = response.get('cause', 'Unknown cause')
            
            return result
        
        time.sleep(delay)
    
    logger.warning(f"Execution did not complete within {max_attempts * delay} seconds")
    return {
        'status': 'UNKNOWN',
        'error': 'Timeout waiting for execution to complete'
    }
