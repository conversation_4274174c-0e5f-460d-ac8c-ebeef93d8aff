from aws_cdk import (
    aws_iam as iam,
    aws_s3 as s3,
    aws_s3_notifications as s3n,
    aws_glue as glue,
    aws_lambda as _lambda,
    aws_stepfunctions as sfn,
    aws_stepfunctions_tasks as tasks,
    aws_athena as athena,
    aws_scheduler as scheduler,
    aws_dynamodb as dynamodb,
    Duration, Tags, Stack, ArnFormat
)
from constructs import Construct

from insighter_commons import json_value_selectors
from insighter_commons.insighter_commons import format_glue_arn
from insighter_commons.constants import (
    RAW_DATA_NETWORK_IMPRESSIONS,
    RAW_DATA_NETWORK_CLICKS,
    RAW_DATA_NETWORK_ACTIVE_VIEWS,
    RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS,
    RAW_DATA_NETWORK_BACKFILL_CLICKS,
    RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS,
    TEMP_TABLE_NETWORK_IMPRESSIONS,
    TEMP_TABLE_NETWORK_CLICKS,
    TEMP_TABLE_NETWORK_ACTIVE_VIEWS,
    TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS,
    TEMP_TABLE_NETWORK_BACKFILL_CLICKS,
    TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS,
    GOOGLE_RAW_DATA_S3_PATH,
    TABLE_MULTI_KEY_VALUE_GOOGLE, DEFAULT_LAMBDA_HANDLER
)

rawDataTablesEnv = {
    "RAW_DATA_NETWORK_IMPRESSIONS": RAW_DATA_NETWORK_IMPRESSIONS,
    "RAW_DATA_NETWORK_CLICKS": RAW_DATA_NETWORK_CLICKS,
    "RAW_DATA_NETWORK_ACTIVE_VIEWS": RAW_DATA_NETWORK_ACTIVE_VIEWS,
    "RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS": RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS,
    "RAW_DATA_NETWORK_BACKFILL_CLICKS": RAW_DATA_NETWORK_BACKFILL_CLICKS,
    "RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS": RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS,
}

tempTablesEnv = {
    "TEMP_TABLE_NETWORK_IMPRESSIONS": TEMP_TABLE_NETWORK_IMPRESSIONS,
    "TEMP_TABLE_NETWORK_CLICKS": TEMP_TABLE_NETWORK_CLICKS,
    "TEMP_TABLE_NETWORK_ACTIVE_VIEWS": TEMP_TABLE_NETWORK_ACTIVE_VIEWS,
    "TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS": TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS,
    "TEMP_TABLE_NETWORK_BACKFILL_CLICKS": TEMP_TABLE_NETWORK_BACKFILL_CLICKS,
    "TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS": TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS,
}


class GoogleFeedConversion(Construct):

    def __init__(
            self,
            scope: Construct,
            construct_id: str,
            glue_crawler_role: iam.IRole,
            processing_bucket: s3.IBucket,
            log_level_data_database: str,
            athena_work_group: athena.CfnWorkGroup,
            athena_result_bucket: s3.IBucket
    ):
        super().__init__(scope, construct_id)

        self.glue_crawler_role = glue_crawler_role
        self.processing_bucket = processing_bucket
        self.log_level_data_database = log_level_data_database
        self.athena_work_group = athena_work_group
        self.athena_results_bucket = athena_result_bucket
        self.query_execution_context = tasks.QueryExecutionContext(
            database_name=log_level_data_database
        )

        Tags.of(self).add("InsighterComponent", "GoogleFeedConversion")

        # Create DynamoDB table for coordinating Google corrected feed processing
        corrected_feed_coordination_table = dynamodb.Table(
            self, "GoogleCorrectedFeedCoordinationTable",
            table_name="GoogleCorrectedFeedCoordination",
            partition_key=dynamodb.Attribute(
                name="partition_key",
                type=dynamodb.AttributeType.STRING
            ),
            billing_mode=dynamodb.BillingMode.ON_DEMAND,
            time_to_live_attribute="ttl",
            point_in_time_recovery=True,
            removal_policy=Stack.of(self).removal_policy
        )

        processing_bucket.grant_read_write(glue_crawler_role)

        self.create_glue_athena_table(
            "NetworkImpressionsFeedCrawler",
            "network_impressions",
            f"/{GOOGLE_RAW_DATA_S3_PATH}{RAW_DATA_NETWORK_IMPRESSIONS}/"
        )

        self.create_glue_athena_table(
            "NetworkBackfillImpressionsFeedCrawler",
            "network_backfill_impressions",
            f"/{GOOGLE_RAW_DATA_S3_PATH}{RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS}/"
        )

        self.create_glue_athena_table(
            "NetworkClicksFeedCrawler",
            "network_clicks",
            f"/{GOOGLE_RAW_DATA_S3_PATH}{RAW_DATA_NETWORK_CLICKS}/"
        )

        self.create_glue_athena_table(
            "NetworkBackfillClicksFeedCrawler",
            "network_backfill_clicks",
            f"/{GOOGLE_RAW_DATA_S3_PATH}{RAW_DATA_NETWORK_BACKFILL_CLICKS}/"
        )

        self.create_glue_athena_table(
            "NetworkActiveViewsFeedCrawler",
            "network_active_views",
            f"/{GOOGLE_RAW_DATA_S3_PATH}{RAW_DATA_NETWORK_ACTIVE_VIEWS}/"
        )

        self.create_glue_athena_table(
            "NetworkBackfillActiveViewsFeedCrawler",
            "network_backfill_active_views",
            f"/{GOOGLE_RAW_DATA_S3_PATH}{RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS}/"
        )

        prepare_athena_table_definition_query_function = _lambda.Function(
            self, 'PrepareAthenaTableDefinitionQueryLambda',
            function_name="insighter-prepare-google-athena-tables-definition-query",
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset('multi_key_value_reports/functions/conversion/google/prepare_athena_table_definition_query'),
            timeout=Duration.seconds(60),
            environment= rawDataTablesEnv | tempTablesEnv | {
                "PROCESSING_BUCKET_NAME": processing_bucket.bucket_name,
                # mkvr table name
                "TABLE_MULTI_KEY_VALUE_GOOGLE": TABLE_MULTI_KEY_VALUE_GOOGLE
            }
        )

        prepare_temp_table_conversion_query_function = _lambda.Function(
            self,
            'PrepareTempTableConversionQueryLambda',
            function_name='insighter-prepare-google-temp-table-conversion-query',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset('multi_key_value_reports/functions/conversion/google/prepare_temp_table_conversion_query'),
            timeout=Duration.seconds(60),
            environment=rawDataTablesEnv
        )

        prepare_mkvr_table_conversion_query_function = _lambda.Function(
            self, 'PrepareMkvrTableConversionQueryLambda',
            function_name='insighter-prepare-google-mkvr-table-conversion-query',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset('multi_key_value_reports/functions/conversion/google/prepare_mkvr_table_conversion_query'),
            timeout=Duration.seconds(60),
            environment=tempTablesEnv | {
                # mkvr table name
                "TABLE_MULTI_KEY_VALUE_GOOGLE": TABLE_MULTI_KEY_VALUE_GOOGLE
            }
        )

        prepare_get_unprocessed_partitions_for_mkvr_function = _lambda.Function(
            self, 'PrepareGetUnprocessedPartitionsForMkvrLambda',
            function_name='insighter-prepare-get-google-mkvr-unprocessed-partitions',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset('multi_key_value_reports/functions/conversion/google/prepare_get_unprocessed_partitions_for_mkvr'),
            timeout=Duration.seconds(60),
            environment=tempTablesEnv | {
                "DATABASE_NAME": log_level_data_database,
                "WORK_GROUP": athena_work_group.name,
                # mkvr table name
                "TABLE_MULTI_KEY_VALUE_GOOGLE": TABLE_MULTI_KEY_VALUE_GOOGLE
            }
        )
        processing_bucket.grant_read(prepare_get_unprocessed_partitions_for_mkvr_function)

        delete_processing_bucket_file_function = _lambda.Function(
            self,
            'DeleteProcessingBucketFileLambda',
            function_name='insighter-delete-processing-bucket-file',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset('multi_key_value_reports/functions/conversion/google/delete_processing_bucket_file'),
            timeout=Duration.seconds(60),
            environment={
                "BUCKET_NAME": processing_bucket.bucket_name
            }
        )

        coordinate_corrected_data_processing_function = _lambda.Function(
            self, 'CoordinateCorrectedDataProcessingLambda',
            function_name='insighter-coordinate-corrected-data-processing',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset('multi_key_value_reports/functions/conversion/google/coordinate_corrected_data_processing'),
            timeout=Duration.minutes(15),  # Increased timeout to wait for step function completion
            environment={
                "COORDINATION_TABLE_NAME": corrected_feed_coordination_table.table_name,
                "TABLE_MULTI_KEY_VALUE_GOOGLE": TABLE_MULTI_KEY_VALUE_GOOGLE
            }
        )

        processing_bucket.grant_read_write(delete_processing_bucket_file_function)

        # Grant DynamoDB permissions to the coordination function
        corrected_feed_coordination_table.grant_read_write_data(coordinate_corrected_data_processing_function)

        # step function for updating google mkvr table with corrected file
        update_mkvr_definition = self.execute_lambda_function_step(
            "Delete outdated partition data from MKVR table",
            delete_processing_bucket_file_function
        ).next(
            self.reload_athena_table_partitions("Reload after MKVR deletion").next(
                self.execute_lambda_function_step(
                    "Prepare 'MKVR insertion query' for corrected data",
                    prepare_mkvr_table_conversion_query_function
                ).next(
                    self.execute_query(
                        'Run "MKVR insertion query" for corrected data',
                        sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                    ).next(
                        self.reload_athena_table_partitions("Reload after MKVR update")
                    )
                )
            )
        )

        update_mkvr_step_function = sfn.StateMachine(
            self, 'GoogleUpdateMkvrStateMachine',
            state_machine_name='Insighter-Google-Update-MKVR-From-Corrected',
            definition_body=sfn.DefinitionBody.from_chainable(update_mkvr_definition)
        )
        update_mkvr_step_function.add_to_role_policy(
            statement=iam.PolicyStatement(
                actions=[
                    "glue:GetDatabase",
                    "glue:GetTables",
                    "glue:GetTable",
                ],
                resources=[
                    self.format_glue_arn("database", "datalake_raw"),
                    self.format_glue_arn("table", "datalake_raw/google_ad_units")
                ]
            )
        )

        # Add the step function ARN to the coordination function environment
        coordinate_corrected_data_processing_function.add_environment(
            "UPDATE_MKVR_STEP_FUNCTION_ARN",
            update_mkvr_step_function.state_machine_arn
        )

        # Grant permission to start the MKVR update step function
        update_mkvr_step_function.grant_start_execution(coordinate_corrected_data_processing_function)

        # Grant EventBridge permissions to the coordination function
        coordinate_corrected_data_processing_function.add_to_role_policy(
            statement=iam.PolicyStatement(
                actions=[
                    "events:PutEvents"
                ],
                resources=["*"]
            )
        )

        # step function for (corrected) raw data to temp table conversion
        temp_tables_conversion_definition = sfn.Choice(
            self,
            "Is Google feeds?"
        ).when(
            sfn.Condition.or_(
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, RAW_DATA_NETWORK_IMPRESSIONS),
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS),
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, RAW_DATA_NETWORK_CLICKS),
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, RAW_DATA_NETWORK_BACKFILL_CLICKS),
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, RAW_DATA_NETWORK_ACTIVE_VIEWS),
                sfn.Condition.string_equals(json_value_selectors.TABLE_NAME, RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS),
            ),
            sfn.Choice(
                self,
                "Is _corrected file or not?"
            ).when(
                # process corrected data to temp table
                sfn.Condition.boolean_equals("$.is_corrected", True),
                sfn.Pass(
                    self, "Start Corrected Data Conversion",
                ).next(
                    self.set_delete_file_path(  # delete outdated converted data
                        "Set path to temp table files",
                        "{}_temp/partition_0={}/partition_1={}/partition_2={}/partition_3={}/"
                    ).next(
                        self.execute_lambda_function_step(
                            "Delete temp table files",
                            delete_processing_bucket_file_function
                        ).next(
                            self.reload_athena_table_partitions("Reload after deletion").next(
                                self.execute_lambda_function_step(
                                    "Prepare 'conversion query' for corrected data",
                                    prepare_temp_table_conversion_query_function
                                ).next(
                                    self.execute_query(
                                        "Run 'conversion query' for corrected data",
                                        sfn.JsonPath.string_at("$.query.QueryString")
                                    ).next(
                                        self.reload_athena_table_partitions("Reload after conversion").next(
                                            self.set_delete_file_path(  # delete converted corrected raw data
                                                "Set path to corrected file",
                                                GOOGLE_RAW_DATA_S3_PATH + "{}/{}/{}/{}/{}/",
                                            ).next(
                                                self.execute_lambda_function_step(
                                                    "Delete corrected file from S3",
                                                    delete_processing_bucket_file_function
                                                ).next(
                                                    self.execute_lambda_function_step(
                                                        "Coordinate corrected data processing",
                                                        coordinate_corrected_data_processing_function
                                                    )
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            ).when(
                # process raw data to temp table
                sfn.Condition.boolean_equals("$.is_corrected", False),
                sfn.Pass(
                    self,
                    "Start Raw Data Conversion",
                    parameters={
                        "bucket_name": sfn.JsonPath.string_at(json_value_selectors.BUCKET_NAME),
                        "table_name": sfn.JsonPath.string_at(json_value_selectors.TABLE_NAME),
                        "path": sfn.JsonPath.string_at(json_value_selectors.S3_PATH),
                        "partition_0": sfn.JsonPath.string_at(json_value_selectors.YEAR_PARTITION),
                        "partition_1": sfn.JsonPath.string_at(json_value_selectors.MONTH_PARTITION),
                        "partition_2": sfn.JsonPath.string_at(json_value_selectors.DAY_PARTITION),
                        "partition_3": sfn.JsonPath.string_at(json_value_selectors.HOUR_PARTITION),
                        "query": {}
                    }
                ).next(
                    self.execute_lambda_function_step(
                        "Prepare 'ensure temp table exists query'",
                        prepare_athena_table_definition_query_function,
                    ).next(
                        self.execute_query(
                            'Run "ensure temp table exists query"',
                            sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                        ).next(
                            self.load_raw_data_partition_query(
                                "Prepare 'load raw data partition query'",
                            ).next(
                                self.execute_query(
                                    "Run 'load raw data partition query'",
                                    sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                                ).next(
                                    self.execute_lambda_function_step(
                                        "Prepare 'conversion query' for raw data",
                                        prepare_temp_table_conversion_query_function
                                    ).next(
                                        self.execute_query(
                                            "Run 'conversion query' for raw data",
                                            sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                                        ).next(
                                            self.load_temp_data_partition_query(
                                                "Prepare 'load temp data partition query'",
                                            ).next(
                                                self.execute_query(
                                                    "Run 'load temp data partition query'",
                                                    sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                                                ).next(
                                                    self.execute_lambda_function_step(
                                                        "Delete raw file from S3",
                                                        delete_processing_bucket_file_function
                                                    )
                                                )
                                            )
                                        )
                                    )
                                )
                            )
                        )
                    )
                )
            )
        ).otherwise(
            sfn.Pass(
                self,
                "Unknown Files"
            )
        )

        temp_tables_conversion_step_function = sfn.StateMachine(
            self, 'GoogleTempTableConversionStateMachine',
            state_machine_name='Insighter-Google-Temp-Tables-Conversion',
            definition_body=sfn.DefinitionBody.from_chainable(temp_tables_conversion_definition)
        )



        trigger_function = _lambda.Function(
            self, 'Trigger Google Feed Conversion Lambda',
            function_name='Insighter-Google-Feed-Conversion-Trigger',
            runtime=_lambda.Runtime.PYTHON_3_12,
            handler=DEFAULT_LAMBDA_HANDLER,
            code=_lambda.Code.from_asset('multi_key_value_reports/functions/conversion/google/step_function_trigger'),
            timeout=Duration.seconds(60),
            environment={
                "CONVERSION_STATE_MACHINE_ARN": temp_tables_conversion_step_function.state_machine_arn,
            }
        )
        processing_bucket.grant_read(trigger_function)
        temp_tables_conversion_step_function.grant_start_execution(trigger_function)

        notification = s3n.LambdaDestination(trigger_function)
        processing_bucket.add_event_notification(
            s3.EventType.OBJECT_CREATED,
            notification,
            s3.NotificationKeyFilter(prefix=GOOGLE_RAW_DATA_S3_PATH, suffix=".gz")
        )

        # step function for joining all temp tables into google mkvr table
        mkvr_conversion_definition = sfn.Pass(
            self,
            "Start MKVR Conversion",
            parameters={
                "table_name": "multi_key_value_google",
                "query": {}
            }
        ).next(
            self.execute_lambda_function_step(
                "Prepare 'ensure MKVR table exists query'",
                prepare_athena_table_definition_query_function
            ).next(
                self.execute_query(
                    "Run 'ensure MKVR table exists query'",
                    sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                ).next(
                    self.execute_lambda_function_step(
                        "Prepare 'get unprocessed partition for MKVR query'",
                        prepare_get_unprocessed_partitions_for_mkvr_function
                    ).next(
                        self.execute_query(
                            "Run 'get unprocessed partition for MKVR query'",
                            sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                        ).next(
                            self.read_athena_result_step(prepare_mkvr_table_conversion_query_function).next(
                                sfn.Map(
                                    self,
                                    "Run 'MKVR insertion query' map",
                                    max_concurrency=5
                                ).add_catch(sfn.Fail(self, "Error when inserting into MKVR")).item_processor(
                                    self.execute_query(
                                        'Run "MKVR insertion query"',
                                        sfn.JsonPath.string_at(json_value_selectors.QUERY_STRING)
                                    )
                                )
                            )
                        )
                    )
                )
            )
        )

        mkvr_conversion_step_function = sfn.StateMachine(
            self, 'GoogleMkvrConversionStateMachine',
            state_machine_name='Insighter-Google-MKVR-Conversions',
            definition_body=sfn.DefinitionBody.from_chainable(mkvr_conversion_definition)
        )
        athena_result_bucket.grant_read_write(mkvr_conversion_step_function)
        processing_bucket.grant_read_write(mkvr_conversion_step_function)
        mkvr_conversion_step_function.add_to_role_policy(
            statement=iam.PolicyStatement(
                actions=[
                    "glue:GetDatabase",
                    "glue:GetTables",
                    "glue:GetTable",
                ],
                resources=[
                    format_glue_arn(self,"database", "datalake_raw"),
                    format_glue_arn(self,"table", "datalake_raw/google_ad_units")
                ]
            )
        )

        scheduler_role = iam.Role(
            self,
            "InsighterGoogleMkvrConversionSchedulerRole",
            assumed_by=iam.ServicePrincipal("scheduler.amazonaws.com")
        )
        scheduler_role.add_to_policy(
            iam.PolicyStatement(
                actions=[
                    "states:StartExecution"
                ],
                resources=[
                    mkvr_conversion_step_function.state_machine_arn
                ]
            )
        )
        scheduler.CfnSchedule(
            self,
            "GoogleMkvrConversionSchedule",
            name="InsighterMkvrConversionSchedule",
            schedule_expression="cron(15 * * * ? *)",
            schedule_expression_timezone="Europe/Berlin",
            flexible_time_window=scheduler.CfnSchedule.FlexibleTimeWindowProperty(
                mode="OFF"
            ),
            target=scheduler.CfnSchedule.TargetProperty(
                arn=mkvr_conversion_step_function.state_machine_arn,
                role_arn=scheduler_role.role_arn
            )
        )

    def create_glue_athena_table(self, construct_id, table_name, bucket_path):
        return glue.CfnCrawler(
            self,
            construct_id,
            name=table_name,
            role=self.glue_crawler_role.role_name,
            database_name=self.log_level_data_database,
            targets=glue.CfnCrawler.TargetsProperty(
                s3_targets=[glue.CfnCrawler.S3TargetProperty(
                    path=f"s3://{self.processing_bucket.bucket_name}{bucket_path}"
                )]
            )
        )

    def load_raw_data_partition_query(self, construct_id):
        return sfn.Pass(
            self,
            construct_id,
            parameters={
                "QueryString":
                    sfn.JsonPath.format(
                        'ALTER TABLE {} ADD IF NOT EXISTS '
                        '   PARTITION ('
                        '       partition_0 = \"{}\", '
                        '       partition_1 = \"{}\", '
                        '       partition_2 = \"{}\", '
                        '       partition_3 = \"{}\") '
                        '       LOCATION \"s3://{}/{}/\"',
                        sfn.JsonPath.string_at(json_value_selectors.TABLE_NAME),
                        sfn.JsonPath.string_at(json_value_selectors.YEAR_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.MONTH_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.DAY_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.HOUR_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.BUCKET_NAME),
                        sfn.JsonPath.string_at(json_value_selectors.S3_PATH)
                    )
            },
            result_path="$.query"
        )

    def load_temp_data_partition_query(self, construct_id):
        return sfn.Pass(
            self,
            construct_id,
            parameters={
                "QueryString":
                    sfn.JsonPath.format(
                        'ALTER TABLE {}_temp ADD IF NOT EXISTS '
                        '   PARTITION ('
                        '       partition_0 = \"{}\", '
                        '       partition_1 = \"{}\", '
                        '       partition_2 = \"{}\", '
                        '       partition_3 = \"{}\") '
                        '       LOCATION \"s3://{}/{}/\"',
                        sfn.JsonPath.string_at(json_value_selectors.TABLE_NAME),
                        sfn.JsonPath.string_at(json_value_selectors.YEAR_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.MONTH_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.DAY_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.HOUR_PARTITION),
                        sfn.JsonPath.string_at(json_value_selectors.BUCKET_NAME),
                        sfn.JsonPath.string_at(json_value_selectors.S3_PATH)
                    )
            },
            result_path="$.query"
        )

    def execute_query(self, construct_id, query_string):
        return tasks.AthenaStartQueryExecution(
            self,
            construct_id,
            query_string=query_string,
            query_execution_context=self.query_execution_context,
            work_group=self.athena_work_group.name,
            result_path=json_value_selectors.OUTPUT_PATH_ATTRIBUTE,
            integration_pattern=sfn.IntegrationPattern.RUN_JOB
        )

    def set_delete_file_path(self, construct_id, path_string):
        return sfn.Pass(
            self,
            construct_id,
            parameters={
                "table_to_reload": sfn.JsonPath.format(
                    "{}_temp",
                    sfn.JsonPath.string_at(json_value_selectors.TABLE_NAME)
                ),
                "path": sfn.JsonPath.format(
                    path_string,
                    sfn.JsonPath.string_at(json_value_selectors.TABLE_NAME),
                    sfn.JsonPath.string_at("$.partition_0"),
                    sfn.JsonPath.string_at("$.partition_1"),
                    sfn.JsonPath.string_at("$.partition_2"),
                    sfn.JsonPath.string_at("$.partition_3")
                ),
                "bucket_name": sfn.JsonPath.string_at(json_value_selectors.BUCKET_NAME),
                "table_name": sfn.JsonPath.string_at(json_value_selectors.TABLE_NAME),
                "partition_0": sfn.JsonPath.string_at("$.partition_0"),
                "partition_1": sfn.JsonPath.string_at("$.partition_1"),
                "partition_2": sfn.JsonPath.string_at("$.partition_2"),
                "partition_3": sfn.JsonPath.string_at("$.partition_3"),
                "query": {}
            },
        )

    def execute_lambda_function_step(self, construct_id, lambda_function):
        return tasks.LambdaInvoke(
            self,
            construct_id,
            lambda_function=lambda_function,
            output_path=json_value_selectors.PAYLOAD
        )

    def reload_athena_table_partitions(self, construct_id):
        return self.execute_query(
            construct_id,
            sfn.JsonPath.format('MSCK REPAIR TABLE {}',
                                sfn.JsonPath.string_at('$.table_to_reload'))
        )

    def read_athena_result_step(self, lambda_function):
        read_results_step = sfn.DistributedMap(
            self,
            "Read results step",
            item_reader=sfn.S3CsvItemReader(
                bucket=self.athena_results_bucket,
                key=sfn.JsonPath.format(
                    "mkvr/{}.csv", sfn.JsonPath.string_at("$.output.QueryExecution.QueryExecutionId")
                ),
                max_items=5
            ),
        ).add_catch(sfn.Succeed(self, "Results file is empty"))

        read_results_step.item_processor(
            tasks.LambdaInvoke(
                self,
                "Prepare 'MKVR insertion query'",
                lambda_function=lambda_function,
                output_path=json_value_selectors.PAYLOAD
            )
        )

        return read_results_step

    def format_glue_arn(self, resource, resource_name):
        return Stack.of(self).format_arn(service="glue", resource=resource, resource_name=resource_name, arn_format=ArnFormat.SLASH_RESOURCE_NAME)
