import os
import unittest
from pathlib import Path

from strictyaml import load
from strictyaml.exceptions import YAMLValidationError

from insighter_commons import REPORT_DEFINITION_SCHEMA

from insighter_commons.constants import get_report_definition_file_paths


class TestReportDefinitionsSchema(unittest.TestCase):

    def test_production_report_definitions_are_valid(self) -> None:
        for file_path in get_report_definition_file_paths(
                root_dir=os.path.dirname(Path(os.path.abspath(__file__)).parent)):
            with open(file_path, "r") as file:
                load(file.read(), REPORT_DEFINITION_SCHEMA)

    def test_schema_validation_fails_on_missing_name(self) -> None:
        file_content = """
        report_timespan:
        data_sources:
          - MICROSOFT
        notification_recipients:
          - <EMAIL>
        sql: SELECT * FROM test
        """

        with self.assertRaises(YAMLValidationError) as e:
            load(file_content, REPORT_DEFINITION_SCHEMA)

        self.assertIn("when expecting one of: REPORTING_LIFETIME, LAST_DAY", str(e.exception.context))

    def test_schema_validation_fails_on_missing_data_sources(self) -> None:
        file_content = """
        name: name
        data_sources:
        notification_recipients:
          - <EMAIL>
        sql: SELECT * FROM test
        """

        with self.assertRaises(YAMLValidationError) as e:
            load(file_content, REPORT_DEFINITION_SCHEMA)

        self.assertIn("when expecting a sequence", str(e.exception.context))

    def test_schema_validation_fails_on_invalid_data_sources(self) -> None:
        file_content = """
        name: name
        data_sources:
          - GOGLE
        notification_recipients:
          - <EMAIL>
        sql: SELECT * FROM test
        """

        with self.assertRaises(YAMLValidationError) as e:
            load(file_content, REPORT_DEFINITION_SCHEMA)

        self.assertIn("when expecting one of: MICROSOFT, GOOGLE", str(e.exception.context))

    def test_notification_recipients_are_optional(self) -> None:
        file_content = """
        name: name
        data_sources:
          - GOOGLE
        sql: SELECT * FROM test
        """
        load(file_content, REPORT_DEFINITION_SCHEMA)

    def test_sharing_account_id_must_be_number(self) -> None:
        file_content = """
        name: name
        data_sources:
          - GOOGLE
        s3_sharing_account_id: abc
        sql: SELECT * FROM test
        """

        with self.assertRaises(YAMLValidationError) as e:
            load(file_content, REPORT_DEFINITION_SCHEMA)

        self.assertIn("when expecting string matching ^\\d{12}$", str(e.exception.context))

    def test_sharing_account_id_must_shorter_than_12_digits(self) -> None:
        file_content = """
        name: name
        data_sources:
          - GOOGLE
        s3_sharing_account_id: *************
        sql: SELECT * FROM test
        """

        with self.assertRaises(YAMLValidationError) as e:
            load(file_content, REPORT_DEFINITION_SCHEMA)

        self.assertIn("when expecting string matching ^\\d{12}$", str(e.exception.context))

    def test_naming_convention(self) -> None:
        file_content = """
        name: Test/invalid
        data_sources:
          - GOOGLE
        sql: SELECT * FROM test
        """

        with self.assertRaises(YAMLValidationError) as e:
            load(file_content, REPORT_DEFINITION_SCHEMA)

        self.assertIn("when expecting string matching ^[a-zA-Z\\-\\s]+$", str(e.exception.context))


if __name__ == '__main__':
    unittest.main()
