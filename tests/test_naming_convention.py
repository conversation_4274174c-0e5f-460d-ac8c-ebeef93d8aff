import os
import unittest
from pathlib import Path

from insighter_commons.constants import get_report_definition_file_paths, REPORT_ID_PATTERN, \
    REPORT_FILE_EXTENSION


class TestReportNaming(unittest.TestCase):

    def test_production_report_names_are_valid(self) -> None:
        for file_path in get_report_definition_file_paths(
                root_dir=os.path.dirname(Path(os.path.abspath(__file__)).parent)):
            file_name_without_extension = os.path.basename(file_path).replace(REPORT_FILE_EXTENSION, "")
            self.assertTrue(REPORT_ID_PATTERN.match(file_name_without_extension))

    def test_production_report_file_extensions_are_valid(self) -> None:
        for file_path in get_report_definition_file_paths(
                root_dir=os.path.dirname(Path(os.path.abspath(__file__)).parent)):
            file_name = os.path.basename(file_path)
            self.assertTrue(file_name.endswith(REPORT_FILE_EXTENSION))



if __name__ == '__main__':
    unittest.main()
