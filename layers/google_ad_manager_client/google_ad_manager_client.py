import boto3
import tempfile
import json
from google.cloud import storage


def __get_google_service_account_credential(credential_name):
    ssm_client = boto3.client(
        service_name="ssm",
        region_name="eu-central-1"
    )

    api_secrets = ssm_client.get_parameter(
        Name=credential_name,
        WithDecryption=True
    )
    return api_secrets["Parameter"]["Value"]


def create_cloud_storage_client(credential_name: str):
    credentials_json = __get_google_service_account_credential(credential_name)

    with (tempfile.TemporaryDirectory() as tmpdir,
          open(tmpdir + '/credentials.json', 'w', encoding='utf-8') as credentials_file):
        parameters = json.loads(credentials_json)
        json_string = json.dumps(parameters, indent=4)
        credentials_file.write(json_string)
        credentials_file.close()
        return storage.Client.from_service_account_json(tmpdir + '/credentials.json')
