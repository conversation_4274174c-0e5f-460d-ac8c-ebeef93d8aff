# Google
import re

CLOUD_STORAGE_NAME = "gdfp-113349434"
GOOGLE_CLOUD_STORAGE_PARAMETER = "google-cloud-platform"

GOOGLE_RAW_DATA_S3_PATH = "tmp/gcs/raw/data/"

RAW_DATA_NETWORK_IMPRESSIONS = "network_impressions"
RAW_DATA_NETWORK_CLICKS = "network_clicks"
RAW_DATA_NETWORK_ACTIVE_VIEWS = "network_active_views"

RAW_DATA_NETWORK_BACKFILL_IMPRESSIONS = "network_backfill_impressions"
RAW_DATA_NETWORK_BACKFILL_CLICKS = "network_backfill_clicks"
RAW_DATA_NETWORK_BACKFILL_ACTIVE_VIEWS = "network_backfill_active_views"

TEMP_TABLE_NETWORK_IMPRESSIONS = "network_impressions_temp"
TEMP_TABLE_NETWORK_CLICKS = "network_clicks_temp"
TEMP_TABLE_NETWORK_ACTIVE_VIEWS = "network_active_views_temp"

TEMP_TABLE_NETWORK_BACKFILL_IMPRESSIONS = "network_backfill_impressions_temp"
TEMP_TABLE_NETWORK_BACKFILL_CLICKS = "network_backfill_clicks_temp"
TEMP_TABLE_NETWORK_BACKFILL_ACTIVE_VIEWS = "network_backfill_active_views_temp"

TABLE_MULTI_KEY_VALUE_GOOGLE = "multi_key_value_google"

# Microsoft
TABLE_MULTI_KEY_VALUE_MICROSOFT = "multi_key_value_microsoft"

# DuckDB
LOAD_CREDENTIALS_STATEMENT = "CREATE OR REPLACE SECRET secret (TYPE S3, PROVIDER CREDENTIAL_CHAIN, REGION 'eu-central-1', ENDPOINT 's3.eu-central-1.amazonaws.com')"


# Reports
DATA_SOURCE_GOOGLE = "GOOGLE"
DATA_SOURCE_MICROSOFT = "MICROSOFT"

def __get_report_definition_file_paths(root_dir) -> [str]:
    paths = []
    from pathlib import Path
    directory = Path("multi_key_value_reports/report_definitions")
    for f in directory.iterdir():
        if f.is_file() and f.suffix == ".yaml":
            paths.append(str(f))

    return paths


get_report_definition_file_paths = __get_report_definition_file_paths

REPORT_FILE_EXTENSION = ".yaml"
REPORT_ID_PATTERN = re.compile(r"^[a-z_]+$")

DEFAULT_LAMBDA_HANDLER = "lambda_function.lambda_handler"
