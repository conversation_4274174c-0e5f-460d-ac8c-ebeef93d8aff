def __schema():
    from strictyaml import Map, Str, Regex, Seq, Datetime, Optional, EmptyNone, Enum
    from datetime import datetime, timedelta

    return Map(
        {
            "name": Regex(REPORT_NAME_PATTERN),
            Optional("start_date", default=datetime.now() - timedelta(days=31)): Datetime(),
            # it's important to keep end_date as None, because it's used to check if report will expire
            Optional("end_date", default=None, drop_if_none=False): EmptyNone() | Datetime(),
            Optional("report_timespan", default="REPORTING_LIFETIME"): Enum(["REPORTING_LIFETIME", "LAST_DAY", "LAST_MONTH"]),
            "data_sources": Seq(Enum(["MICROSOFT", "GOOGLE"])),
            Optional("notification_recipients"): Seq(Str()),
            Optional("s3_sharing_account_id"): Regex(r"^\d{12}$"),
            "sql": Str(),
            Optional("ecs_memory", default="32768"): Str(),
        }
    )

REPORT_NAME_PATTERN = r"^[a-zA-Z\-\s]+$"
REPORT_DEFINITION_SCHEMA = __schema()


def format_glue_arn(self, resource, resource_name):
    from aws_cdk import Stack, ArnFormat

    return Stack.of(self).format_arn(service="glue", resource=resource, resource_name=resource_name, arn_format=ArnFormat.SLASH_RESOURCE_NAME)
