[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "insighter_commons"
version = "1.0.0"
dependencies = [
    "strictyaml>=1.7.3"
]

[tool.poetry]
name = "insighter_commons"
version = "1.0.0"
description = ""
authors = ["Business Integration"]

[tool.poetry.dependencies]
python="^3.12"
strictyaml="1.7.3"
aws-cdk-lib="2.181.1"

[tool.poetry.requires-plugins]
poetry-plugin-export = ">=1.8"
